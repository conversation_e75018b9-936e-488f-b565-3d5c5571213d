{"update_url": "https://clients2.google.com/service/update2/crx", "manifest_version": 3, "version": "8.0.7", "name": "CthulhuJs (Anti-Fingerprint)", "description": "Your browser, your rules — your digital fingerprint, in your control.", "homepage_url": "https://lxs2000.github.io/cthulhurs-doc/dist/zh/", "default_locale": "en", "permissions": ["storage", "tabs", "browsingData", "activeTab", "background", "scripting", "webNavigation", "notifications", "declarativeNetRequest"], "host_permissions": ["<all_urls>"], "icons": {"16": "assets/logo.png", "32": "assets/logo.png", "48": "assets/logo.png", "128": "assets/logo.png"}, "content_scripts": [], "background": {"service_worker": "background/background.js"}, "action": {"default_icon": "assets/logo.png", "default_popup": "index.html"}, "web_accessible_resources": [{"resources": ["img/*", "icon/*", "js/*"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; frame-src 'self'; child-src 'self';"}}