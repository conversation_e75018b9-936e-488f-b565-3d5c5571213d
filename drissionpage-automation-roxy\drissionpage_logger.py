import os
import json
import time
from datetime import datetime
from pathlib import Path

class DrissionPageLogger:
    """
    DrissionPage Logger
    记录自动化过程中的日志、截图和HTML
    """
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.logs_dir = self.base_dir / 'logs'
        self.screenshots_dir = self.base_dir / 'screenshots'
        self.html_dir = self.base_dir / 'html'
        
        # 创建目录
        self.logs_dir.mkdir(exist_ok=True)
        self.screenshots_dir.mkdir(exist_ok=True)
        self.html_dir.mkdir(exist_ok=True)
        
        # 日志文件
        today = datetime.now().strftime('%Y-%m-%d')
        self.log_file = self.logs_dir / f'drissionpage-{today}.log'
        
        # 计数器
        self.step_counter = 0
        self.start_time = time.time()
        
        self.log('🤖 DrissionPage Logger 初始化完成')
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f'[{timestamp}] {message}'
        
        # 输出到控制台
        print(log_entry)
        
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    
    def error(self, message, error=None):
        """记录错误日志"""
        error_msg = f'❌ {message}'
        if error:
            error_msg += f': {str(error)}'
        self.log(error_msg)
    
    def warn(self, message):
        """记录警告日志"""
        self.log(f'⚠️ {message}')
    
    def capture_step(self, page, step_name, description, logical_step: int | None = None):
        """捕获步骤截图和HTML
        logical_step: 业务步骤序号（如 7 表示“点击Continue”），用于文件名前缀，便于对齐业务步骤
        """
        self.step_counter += 1
        prefix = f'L{logical_step:02d}_' if isinstance(logical_step, int) else ''
        step_id = f'{prefix}C{self.step_counter:02d}_{step_name}'

        try:
            # 保存截图 - 增加超时处理
            screenshot_path = self.screenshots_dir / f'{step_id}.png'
            try:
                page.get_screenshot(str(screenshot_path))  # 移除timeout参数
            except Exception as screenshot_error:
                self.log(f'⚠️ 截图失败 {step_id}: {str(screenshot_error)}')
                # 截图失败不影响继续执行

            # 保存HTML - 增加异常处理
            html_path = self.html_dir / f'{step_id}.html'
            try:
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(page.html)
            except Exception as html_error:
                self.log(f'⚠️ HTML保存失败 {step_id}: {str(html_error)}')
                # HTML保存失败也不影响继续执行

            if logical_step is not None:
                self.log(f'📸 步骤 {logical_step} (Capture {self.step_counter}): {description}')
            else:
                self.log(f'📸 步骤 {self.step_counter}: {description}')

        except Exception as e:
            self.error(f'捕获步骤失败 {step_id}', e)
    
    def capture_error(self, page, error_name, description):
        """捕获错误截图和HTML"""
        error_id = f'ERROR_{self.step_counter + 1}_{error_name}'
        
        try:
            # 保存截图
            screenshot_path = self.screenshots_dir / f'{error_id}.png'
            page.get_screenshot(str(screenshot_path))
            
            # 保存HTML
            html_path = self.html_dir / f'{error_id}.html'
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(page.html)
            
            self.error(f'错误捕获: {description}')
            
        except Exception as e:
            self.error(f'捕获错误失败 {error_id}', e)
    
    def log_flow_start(self):
        """记录流程开始"""
        self.log('🚀 DrissionPage 自动化流程开始')
        self.start_time = time.time()
    
    def log_flow_end(self, success=True):
        """记录流程结束"""
        duration = time.time() - self.start_time
        status = '成功' if success else '失败'
        self.log(f'🏁 DrissionPage 自动化流程{status}，耗时: {duration:.2f}秒')
    
    def get_stats(self):
        """获取统计信息"""
        duration = time.time() - self.start_time
        return {
            'step_count': self.step_counter,
            'duration': duration,
            'duration_str': f'{duration:.2f}秒',
            'screenshots_dir': str(self.screenshots_dir),
            'html_dir': str(self.html_dir),
            'log_file': str(self.log_file)
        }
